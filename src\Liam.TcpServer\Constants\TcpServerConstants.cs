namespace Liam.TcpServer.Constants;

/// <summary>
/// TCP服务器相关常量定义
/// </summary>
public static class TcpServerConstants
{
    /// <summary>
    /// 默认配置常量
    /// </summary>
    public static class Defaults
    {
        /// <summary>
        /// 默认端口号
        /// </summary>
        public const int Port = 8080;

        /// <summary>
        /// 默认最大连接数
        /// </summary>
        public const int MaxConnections = 1000;

        /// <summary>
        /// 默认接收缓冲区大小 (8KB)
        /// </summary>
        public const int ReceiveBufferSize = 8192;

        /// <summary>
        /// 默认发送缓冲区大小 (8KB)
        /// </summary>
        public const int SendBufferSize = 8192;

        /// <summary>
        /// 默认连接超时时间 (30秒)
        /// </summary>
        public const int ConnectionTimeoutSeconds = 30;

        /// <summary>
        /// 默认心跳间隔 (60秒)
        /// </summary>
        public const int HeartbeatIntervalSeconds = 60;

        /// <summary>
        /// 默认心跳超时时间 (10秒)
        /// </summary>
        public const int HeartbeatTimeoutSeconds = 10;

        /// <summary>
        /// 默认消息最大长度 (1MB)
        /// </summary>
        public const int MaxMessageLength = 1024 * 1024;

        /// <summary>
        /// 默认连接队列长度
        /// </summary>
        public const int ConnectionBacklog = 100;
    }

    /// <summary>
    /// 消息类型常量
    /// </summary>
    public static class MessageTypes
    {
        /// <summary>
        /// 普通数据消息
        /// </summary>
        public const byte Data = 0x01;

        /// <summary>
        /// 心跳请求消息
        /// </summary>
        public const byte HeartbeatRequest = 0x02;

        /// <summary>
        /// 心跳响应消息
        /// </summary>
        public const byte HeartbeatResponse = 0x03;

        /// <summary>
        /// 连接确认消息
        /// </summary>
        public const byte ConnectionAck = 0x04;

        /// <summary>
        /// 断开连接消息
        /// </summary>
        public const byte Disconnect = 0x05;

        /// <summary>
        /// 错误消息
        /// </summary>
        public const byte Error = 0xFF;
    }

    /// <summary>
    /// 连接状态常量
    /// </summary>
    public static class ConnectionStates
    {
        /// <summary>
        /// 连接中
        /// </summary>
        public const string Connecting = "Connecting";

        /// <summary>
        /// 已连接
        /// </summary>
        public const string Connected = "Connected";

        /// <summary>
        /// 断开连接中
        /// </summary>
        public const string Disconnecting = "Disconnecting";

        /// <summary>
        /// 已断开
        /// </summary>
        public const string Disconnected = "Disconnected";

        /// <summary>
        /// 错误状态
        /// </summary>
        public const string Error = "Error";
    }

    /// <summary>
    /// 安全相关常量
    /// </summary>
    public static class Security
    {
        /// <summary>
        /// 默认连接频率限制 (每分钟最大连接数)
        /// </summary>
        public const int DefaultConnectionRateLimit = 60;

        /// <summary>
        /// 默认IP黑名单检查间隔 (分钟)
        /// </summary>
        public const int BlacklistCheckIntervalMinutes = 5;

        /// <summary>
        /// 默认SSL握手超时时间 (秒)
        /// </summary>
        public const int SslHandshakeTimeoutSeconds = 30;
    }

    /// <summary>
    /// 性能监控相关常量
    /// </summary>
    public static class Performance
    {
        /// <summary>
        /// 统计信息更新间隔 (秒)
        /// </summary>
        public const int StatisticsUpdateIntervalSeconds = 10;

        /// <summary>
        /// 性能计数器采样间隔 (毫秒)
        /// </summary>
        public const int PerformanceCounterSampleIntervalMs = 1000;

        /// <summary>
        /// 内存池最大对象数量
        /// </summary>
        public const int MemoryPoolMaxObjects = 1000;
    }
}
