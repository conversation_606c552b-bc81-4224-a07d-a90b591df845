using System.Collections.Concurrent;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using Microsoft.Extensions.Logging;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;

namespace Liam.TcpServer.Services;

/// <summary>
/// 安全管理器实现
/// </summary>
public class SecurityManager : ISecurityManager
{
    private readonly SecuritySettings _settings;
    private readonly ILogger<SecurityManager>? _logger;
    private readonly ConcurrentDictionary<string, ConnectionAttemptRecord> _connectionAttempts = new();
    private readonly ConcurrentDictionary<string, DateTime> _temporaryBlacklist = new();
    private readonly Timer? _cleanupTimer;
    private bool _disposed = false;

    // 统计信息
    private long _totalConnectionAttempts = 0;
    private long _successfulConnections = 0;
    private long _rejectedConnections = 0;
    private long _blacklistBlocks = 0;
    private long _whitelistPasses = 0;
    private long _rateLimitBlocks = 0;
    private long _authenticationSuccesses = 0;
    private long _authenticationFailures = 0;
    private long _sslHandshakeSuccesses = 0;
    private long _sslHandshakeFailures = 0;

    /// <summary>
    /// 安全设置
    /// </summary>
    public SecuritySettings Settings => _settings;

    /// <summary>
    /// 初始化安全管理器
    /// </summary>
    /// <param name="settings">安全设置</param>
    /// <param name="logger">日志记录器</param>
    public SecurityManager(SecuritySettings settings, ILogger<SecurityManager>? logger = null)
    {
        _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        _logger = logger;

        // 创建清理定时器
        var cleanupInterval = TimeSpan.FromMinutes(_settings.BlacklistCheckIntervalMinutes);
        _cleanupTimer = new Timer(CleanupCallback, null, cleanupInterval, cleanupInterval);

        _logger?.LogInformation("安全管理器已初始化");
    }

    /// <summary>
    /// 检查IP是否被允许连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否被允许连接</returns>
    public bool IsConnectionAllowed(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);

        Interlocked.Increment(ref _totalConnectionAttempts);

        try
        {
            // 检查临时黑名单
            var ipString = ipAddress.ToString();
            if (_temporaryBlacklist.ContainsKey(ipString))
            {
                Interlocked.Increment(ref _blacklistBlocks);
                Interlocked.Increment(ref _rejectedConnections);
                _logger?.LogWarning("IP {IpAddress} 在临时黑名单中，拒绝连接", ipAddress);
                return false;
            }

            // 检查永久黑名单
            if (_settings.IsInBlacklist(ipAddress))
            {
                Interlocked.Increment(ref _blacklistBlocks);
                Interlocked.Increment(ref _rejectedConnections);
                _logger?.LogWarning("IP {IpAddress} 在黑名单中，拒绝连接", ipAddress);
                return false;
            }

            // 检查白名单
            if (_settings.EnableWhitelist && !_settings.IsInWhitelist(ipAddress))
            {
                Interlocked.Increment(ref _rejectedConnections);
                _logger?.LogWarning("IP {IpAddress} 不在白名单中，拒绝连接", ipAddress);
                return false;
            }

            // 检查连接频率限制
            if (_settings.EnableConnectionRateLimit && IsConnectionRateLimited(ipAddress))
            {
                Interlocked.Increment(ref _rateLimitBlocks);
                Interlocked.Increment(ref _rejectedConnections);
                _logger?.LogWarning("IP {IpAddress} 连接频率超限，拒绝连接", ipAddress);
                return false;
            }

            // 通过所有检查
            if (_settings.EnableWhitelist)
            {
                Interlocked.Increment(ref _whitelistPasses);
            }
            
            Interlocked.Increment(ref _successfulConnections);
            _logger?.LogDebug("IP {IpAddress} 通过安全检查，允许连接", ipAddress);
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "检查IP {IpAddress} 连接权限时发生异常", ipAddress);
            Interlocked.Increment(ref _rejectedConnections);
            return false;
        }
    }

    /// <summary>
    /// 检查连接频率是否超限
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否超限</returns>
    public bool IsConnectionRateLimited(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);

        if (!_settings.EnableConnectionRateLimit)
        {
            return false;
        }

        var ipString = ipAddress.ToString();
        var now = DateTime.UtcNow;
        var windowStart = now.AddMinutes(-_settings.RateLimitWindowMinutes);

        var record = _connectionAttempts.GetOrAdd(ipString, _ => new ConnectionAttemptRecord());

        lock (record)
        {
            // 清理过期的连接记录
            record.Attempts.RemoveAll(attempt => attempt < windowStart);

            // 检查是否超过限制
            if (record.Attempts.Count >= _settings.ConnectionRateLimit)
            {
                // 如果启用自动黑名单，将IP加入临时黑名单
                if (_settings.EnableAutoBlacklist && record.Attempts.Count >= _settings.AutoBlacklistThreshold)
                {
                    var blacklistUntil = now.AddMinutes(_settings.AutoBlacklistDurationMinutes);
                    _temporaryBlacklist.TryAdd(ipString, blacklistUntil);
                    _logger?.LogWarning("IP {IpAddress} 因频繁连接被加入临时黑名单，持续到 {BlacklistUntil}", 
                        ipAddress, blacklistUntil);
                }

                return true;
            }

            // 记录本次连接尝试
            record.Attempts.Add(now);
            return false;
        }
    }

    /// <summary>
    /// 记录连接尝试
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="isSuccess">是否成功</param>
    public void RecordConnectionAttempt(IPAddress ipAddress, bool isSuccess)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);

        var ipString = ipAddress.ToString();
        var record = _connectionAttempts.GetOrAdd(ipString, _ => new ConnectionAttemptRecord());

        lock (record)
        {
            if (isSuccess)
            {
                record.SuccessfulConnections++;
            }
            else
            {
                record.FailedConnections++;
            }
            record.LastAttempt = DateTime.UtcNow;
        }

        _logger?.LogDebug("记录IP {IpAddress} 连接尝试，结果: {Result}", ipAddress, isSuccess ? "成功" : "失败");
    }

    /// <summary>
    /// 添加IP到白名单
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void AddToWhitelist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        _settings.AddToWhitelist(ipAddress);
        _logger?.LogInformation("IP {IpAddress} 已添加到白名单", ipAddress);
    }

    /// <summary>
    /// 添加IP到黑名单
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="duration">黑名单持续时间（可选）</param>
    public void AddToBlacklist(IPAddress ipAddress, TimeSpan? duration = null)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);

        if (duration.HasValue)
        {
            // 添加到临时黑名单
            var ipString = ipAddress.ToString();
            var blacklistUntil = DateTime.UtcNow.Add(duration.Value);
            _temporaryBlacklist.TryAdd(ipString, blacklistUntil);
            _logger?.LogInformation("IP {IpAddress} 已添加到临时黑名单，持续到 {BlacklistUntil}", ipAddress, blacklistUntil);
        }
        else
        {
            // 添加到永久黑名单
            _settings.AddToBlacklist(ipAddress);
            _logger?.LogInformation("IP {IpAddress} 已添加到永久黑名单", ipAddress);
        }
    }

    /// <summary>
    /// 从白名单移除IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void RemoveFromWhitelist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        _settings.RemoveFromWhitelist(ipAddress);
        _logger?.LogInformation("IP {IpAddress} 已从白名单移除", ipAddress);
    }

    /// <summary>
    /// 从黑名单移除IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    public void RemoveFromBlacklist(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        
        var ipString = ipAddress.ToString();
        
        // 从永久黑名单移除
        _settings.RemoveFromBlacklist(ipAddress);
        
        // 从临时黑名单移除
        _temporaryBlacklist.TryRemove(ipString, out _);
        
        _logger?.LogInformation("IP {IpAddress} 已从黑名单移除", ipAddress);
    }

    /// <summary>
    /// 清理过期的黑名单条目
    /// </summary>
    /// <returns>清理的条目数</returns>
    public int CleanupExpiredBlacklistEntries()
    {
        var now = DateTime.UtcNow;
        var expiredEntries = _temporaryBlacklist
            .Where(kvp => kvp.Value <= now)
            .Select(kvp => kvp.Key)
            .ToList();

        int cleanedCount = 0;
        foreach (var ipString in expiredEntries)
        {
            if (_temporaryBlacklist.TryRemove(ipString, out _))
            {
                cleanedCount++;
                _logger?.LogDebug("IP {IpAddress} 已从临时黑名单中过期移除", ipString);
            }
        }

        if (cleanedCount > 0)
        {
            _logger?.LogInformation("清理了 {CleanedCount} 个过期的临时黑名单条目", cleanedCount);
        }

        return cleanedCount;
    }

    /// <summary>
    /// 验证SSL证书
    /// </summary>
    /// <param name="certificate">证书</param>
    /// <param name="chain">证书链</param>
    /// <param name="sslPolicyErrors">SSL策略错误</param>
    /// <returns>是否验证通过</returns>
    public bool ValidateSslCertificate(X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
    {
        ArgumentNullException.ThrowIfNull(certificate);
        ArgumentNullException.ThrowIfNull(chain);

        try
        {
            // 如果没有错误，直接通过
            if (sslPolicyErrors == SslPolicyErrors.None)
            {
                Interlocked.Increment(ref _sslHandshakeSuccesses);
                return true;
            }

            // 这里可以添加自定义的证书验证逻辑
            // 例如：允许自签名证书、忽略证书名称不匹配等

            _logger?.LogWarning("SSL证书验证失败: {SslPolicyErrors}", sslPolicyErrors);
            Interlocked.Increment(ref _sslHandshakeFailures);
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "验证SSL证书时发生异常");
            Interlocked.Increment(ref _sslHandshakeFailures);
            return false;
        }
    }

    /// <summary>
    /// 创建SSL流
    /// </summary>
    /// <param name="networkStream">网络流</param>
    /// <param name="serverCertificate">服务器证书</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>SSL流</returns>
    public async Task<SslStream> CreateSslStreamAsync(Stream networkStream, X509Certificate2 serverCertificate, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(networkStream);
        ArgumentNullException.ThrowIfNull(serverCertificate);

        try
        {
            var sslStream = new SslStream(networkStream, false,
                (sender, certificate, chain, sslPolicyErrors) =>
                {
                    if (certificate == null || chain == null)
                        return false;
                    return ValidateSslCertificate(certificate, chain, sslPolicyErrors);
                });

            await sslStream.AuthenticateAsServerAsync(serverCertificate, false, System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13, false).ConfigureAwait(false);

            Interlocked.Increment(ref _sslHandshakeSuccesses);
            _logger?.LogDebug("SSL握手成功");

            return sslStream;
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _sslHandshakeFailures);
            _logger?.LogError(ex, "创建SSL流时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 验证客户端认证
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="credentials">认证凭据</param>
    /// <returns>认证结果</returns>
    public Task<AuthenticationResult> ValidateAuthenticationAsync(ClientConnection connection, AuthenticationCredentials credentials)
    {
        ArgumentNullException.ThrowIfNull(connection);
        ArgumentNullException.ThrowIfNull(credentials);

        try
        {
            // 简单的密钥认证
            if (!string.IsNullOrEmpty(credentials.Key) && !string.IsNullOrEmpty(_settings.AuthenticationKey))
            {
                if (credentials.Key == _settings.AuthenticationKey)
                {
                    Interlocked.Increment(ref _authenticationSuccesses);
                    _logger?.LogInformation("连接 {ConnectionId} 认证成功", connection.Id);
                    return Task.FromResult(AuthenticationResult.Success("authenticated-user"));
                }
            }

            // 用户名密码认证
            if (!string.IsNullOrEmpty(credentials.Username) && !string.IsNullOrEmpty(credentials.Password))
            {
                // 实现安全的密码验证逻辑
                if (await ValidateUserCredentialsAsync(credentials.Username, credentials.Password).ConfigureAwait(false))
                {
                    Interlocked.Increment(ref _authenticationSuccesses);
                    _logger?.LogInformation("连接 {ConnectionId} 用户 {Username} 认证成功", connection.Id, credentials.Username);
                    return AuthenticationResult.Success(credentials.Username);
                }
            }

            Interlocked.Increment(ref _authenticationFailures);
            _logger?.LogWarning("连接 {ConnectionId} 认证失败", connection.Id);
            return Task.FromResult(AuthenticationResult.Failure("认证失败"));
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _authenticationFailures);
            _logger?.LogError(ex, "验证认证时发生异常");
            return Task.FromResult(AuthenticationResult.Failure($"认证异常: {ex.Message}"));
        }
    }

    /// <summary>
    /// 验证用户凭据（安全的密码验证）
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>是否验证通过</returns>
    private async Task<bool> ValidateUserCredentialsAsync(string username, string password)
    {
        // 在实际应用中，这里应该：
        // 1. 从数据库或配置中获取用户的密码哈希
        // 2. 使用安全的密码哈希算法（如 BCrypt、Argon2、PBKDF2）验证密码
        // 3. 实现防暴力破解机制（如账户锁定、延迟响应等）

        try
        {
            // 示例：从配置中获取预定义的用户凭据
            var validUsers = _settings.ValidUsers ?? new Dictionary<string, string>();

            if (validUsers.TryGetValue(username, out var storedPasswordHash))
            {
                // 使用安全的密码验证（这里简化为示例，实际应使用 BCrypt 等）
                return await Task.Run(() => VerifyPassword(password, storedPasswordHash)).ConfigureAwait(false);
            }

            // 防止时序攻击：即使用户不存在也执行相同的计算时间
            await Task.Run(() => VerifyPassword(password, "dummy_hash")).ConfigureAwait(false);
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "验证用户凭据时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 验证密码（简化版本，实际应使用 BCrypt 等安全算法）
    /// </summary>
    /// <param name="password">明文密码</param>
    /// <param name="storedHash">存储的密码哈希</param>
    /// <returns>是否匹配</returns>
    private static bool VerifyPassword(string password, string storedHash)
    {
        // 注意：这是一个简化的示例实现
        // 在生产环境中，应该使用 BCrypt、Argon2 或 PBKDF2 等安全的密码哈希算法

        // 简单的 SHA256 + 盐值验证（仅用于演示）
        using var sha256 = SHA256.Create();
        var passwordBytes = Encoding.UTF8.GetBytes(password + "salt_value"); // 应使用随机盐值
        var hashBytes = sha256.ComputeHash(passwordBytes);
        var computedHash = Convert.ToBase64String(hashBytes);

        return computedHash == storedHash;
    }

    /// <summary>
    /// 加密消息
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>加密后的数据</returns>
    public byte[] EncryptMessage(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (!_settings.EnableMessageEncryption || _settings.EncryptionKey == null)
        {
            return data;
        }

        try
        {
            using var aes = Aes.Create();
            aes.Key = _settings.EncryptionKey;
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            
            // 写入IV
            msEncrypt.Write(aes.IV, 0, aes.IV.Length);
            
            using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
            {
                csEncrypt.Write(data, 0, data.Length);
            }

            return msEncrypt.ToArray();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "加密消息时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 解密消息
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <returns>解密后的数据</returns>
    public byte[] DecryptMessage(byte[] encryptedData)
    {
        ArgumentNullException.ThrowIfNull(encryptedData);

        if (!_settings.EnableMessageEncryption || _settings.EncryptionKey == null)
        {
            return encryptedData;
        }

        try
        {
            using var aes = Aes.Create();
            aes.Key = _settings.EncryptionKey;

            // 提取IV
            var iv = new byte[aes.IV.Length];
            Array.Copy(encryptedData, 0, iv, 0, iv.Length);
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(encryptedData, iv.Length, encryptedData.Length - iv.Length);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var msResult = new MemoryStream();
            
            csDecrypt.CopyTo(msResult);
            return msResult.ToArray();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "解密消息时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 生成认证令牌
    /// </summary>
    /// <param name="user">用户</param>
    /// <param name="expiresAt">过期时间</param>
    /// <returns>认证令牌</returns>
    public string GenerateAuthenticationToken(string user, DateTime? expiresAt = null)
    {
        ArgumentNullException.ThrowIfNull(user);

        try
        {
            var expires = expiresAt ?? DateTime.UtcNow.AddHours(24);
            var tokenData = $"{user}|{expires:O}|{Guid.NewGuid():N}";
            var tokenBytes = Encoding.UTF8.GetBytes(tokenData);
            
            // 简单的Base64编码（实际应用中应使用JWT或其他安全令牌格式）
            return Convert.ToBase64String(tokenBytes);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "生成认证令牌时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 验证认证令牌
    /// </summary>
    /// <param name="token">认证令牌</param>
    /// <returns>令牌验证结果</returns>
    public TokenValidationResult ValidateAuthenticationToken(string token)
    {
        ArgumentNullException.ThrowIfNull(token);

        try
        {
            var tokenBytes = Convert.FromBase64String(token);
            var tokenData = Encoding.UTF8.GetString(tokenBytes);
            var parts = tokenData.Split('|');

            if (parts.Length != 3)
            {
                return TokenValidationResult.Failure("令牌格式无效");
            }

            var user = parts[0];
            if (!DateTime.TryParse(parts[1], out var expiresAt))
            {
                return TokenValidationResult.Failure("令牌过期时间无效");
            }

            var isExpired = DateTime.UtcNow > expiresAt;
            if (isExpired)
            {
                return TokenValidationResult.Failure("令牌已过期", true);
            }

            return TokenValidationResult.Success(user, expiresAt);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "验证认证令牌时发生异常");
            return TokenValidationResult.Failure($"令牌验证异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取安全统计信息
    /// </summary>
    /// <returns>安全统计信息</returns>
    public SecurityStatistics GetStatistics()
    {
        return new SecurityStatistics
        {
            TotalConnectionAttempts = _totalConnectionAttempts,
            SuccessfulConnections = _successfulConnections,
            RejectedConnections = _rejectedConnections,
            BlacklistBlocks = _blacklistBlocks,
            WhitelistPasses = _whitelistPasses,
            RateLimitBlocks = _rateLimitBlocks,
            AuthenticationSuccesses = _authenticationSuccesses,
            AuthenticationFailures = _authenticationFailures,
            SslHandshakeSuccesses = _sslHandshakeSuccesses,
            SslHandshakeFailures = _sslHandshakeFailures,
            WhitelistEntries = _settings.Whitelist.Count,
            BlacklistEntries = _settings.Blacklist.Count + _temporaryBlacklist.Count,
            LastUpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 检查连接是否需要认证
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>是否需要认证</returns>
    public bool RequiresAuthentication(ClientConnection connection)
    {
        ArgumentNullException.ThrowIfNull(connection);
        return _settings.EnableAuthentication && !connection.IsAuthenticated;
    }

    /// <summary>
    /// 检查连接认证是否过期
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>是否过期</returns>
    public bool IsAuthenticationExpired(ClientConnection connection)
    {
        ArgumentNullException.ThrowIfNull(connection);

        if (!connection.IsAuthenticated)
        {
            return false;
        }

        // 检查认证超时
        var authTimeout = TimeSpan.FromSeconds(_settings.AuthenticationTimeoutSeconds);
        var authExpired = DateTime.UtcNow - connection.ConnectedAt > authTimeout;

        return authExpired;
    }

    /// <summary>
    /// 清理定时器回调
    /// </summary>
    /// <param name="state">状态对象</param>
    private void CleanupCallback(object? state)
    {
        if (_disposed)
        {
            return;
        }

        try
        {
            CleanupExpiredBlacklistEntries();
            CleanupOldConnectionAttempts();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "清理安全数据时发生异常");
        }
    }

    /// <summary>
    /// 清理旧的连接尝试记录
    /// </summary>
    private void CleanupOldConnectionAttempts()
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-24); // 保留24小时内的记录
        var expiredIps = new List<string>();

        foreach (var kvp in _connectionAttempts)
        {
            var record = kvp.Value;
            lock (record)
            {
                if (record.LastAttempt < cutoffTime)
                {
                    expiredIps.Add(kvp.Key);
                }
            }
        }

        foreach (var ip in expiredIps)
        {
            _connectionAttempts.TryRemove(ip, out _);
        }

        if (expiredIps.Count > 0)
        {
            _logger?.LogDebug("清理了 {Count} 个过期的连接尝试记录", expiredIps.Count);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 异步释放资源
    /// </summary>
    /// <returns>释放任务</returns>
    public async ValueTask DisposeAsync()
    {
        await DisposeAsyncCore().ConfigureAwait(false);
        Dispose(false);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _cleanupTimer?.Dispose();
            _disposed = true;
            _logger?.LogInformation("安全管理器已停止");
        }
    }

    /// <summary>
    /// 异步释放资源核心方法
    /// </summary>
    /// <returns>释放任务</returns>
    protected virtual async ValueTask DisposeAsyncCore()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _disposed = true;
            _logger?.LogInformation("安全管理器已异步停止");
            await Task.CompletedTask.ConfigureAwait(false);
        }
    }
}

/// <summary>
/// 连接尝试记录
/// </summary>
internal class ConnectionAttemptRecord
{
    /// <summary>
    /// 连接尝试时间列表
    /// </summary>
    public List<DateTime> Attempts { get; } = new();

    /// <summary>
    /// 成功连接数
    /// </summary>
    public long SuccessfulConnections { get; set; }

    /// <summary>
    /// 失败连接数
    /// </summary>
    public long FailedConnections { get; set; }

    /// <summary>
    /// 最后尝试时间
    /// </summary>
    public DateTime LastAttempt { get; set; }
}
