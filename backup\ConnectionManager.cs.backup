using System.Collections.Concurrent;
using System.Net;
using Microsoft.Extensions.Logging;
using Liam.TcpServer.Constants;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;

namespace Liam.TcpServer.Services;

/// <summary>
/// 连接管理器实现
/// </summary>
public class ConnectionManager : IConnectionManager
{
    private readonly ConcurrentDictionary<string, ClientConnection> _connections = new();
    private readonly ILogger<ConnectionManager>? _logger;
    private readonly object _statisticsLock = new();
    private bool _disposed = false;

    // 统计信息
    private long _totalConnections = 0;
    private int _peakConnections = 0;
    private readonly List<DateTime> _connectionHistory = new();

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int ConnectionCount => _connections.Count;

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; }

    /// <summary>
    /// 是否已达到最大连接数
    /// </summary>
    public bool IsMaxConnectionsReached => ConnectionCount >= MaxConnections;

    /// <summary>
    /// 初始化连接管理器
    /// </summary>
    /// <param name="maxConnections">最大连接数</param>
    /// <param name="logger">日志记录器</param>
    public ConnectionManager(int maxConnections = TcpServerConstants.Defaults.MaxConnections, ILogger<ConnectionManager>? logger = null)
    {
        MaxConnections = maxConnections > 0 ? maxConnections : TcpServerConstants.Defaults.MaxConnections;
        _logger = logger;
    }

    /// <summary>
    /// 添加连接
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>是否成功添加</returns>
    public bool AddConnection(ClientConnection connection)
    {
        ArgumentNullException.ThrowIfNull(connection);

        if (IsMaxConnectionsReached)
        {
            _logger?.LogWarning("已达到最大连接数限制 {MaxConnections}，拒绝新连接 {ConnectionId}", MaxConnections, connection.Id);
            return false;
        }

        if (_connections.TryAdd(connection.Id, connection))
        {
            lock (_statisticsLock)
            {
                _totalConnections++;
                _connectionHistory.Add(DateTime.UtcNow);
                
                var currentCount = ConnectionCount;
                if (currentCount > _peakConnections)
                {
                    _peakConnections = currentCount;
                }
            }

            _logger?.LogInformation("添加新连接 {ConnectionId} from {ClientAddress}，当前连接数: {ConnectionCount}", 
                connection.Id, connection.ClientIpAddress, ConnectionCount);
            
            return true;
        }

        _logger?.LogWarning("连接 {ConnectionId} 已存在，无法添加", connection.Id);
        return false;
    }

    /// <summary>
    /// 移除连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveConnection(string connectionId)
    {
        ArgumentNullException.ThrowIfNull(connectionId);

        if (_connections.TryRemove(connectionId, out var connection))
        {
            try
            {
                connection.Dispose();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "释放连接 {ConnectionId} 时发生异常", connectionId);
            }

            _logger?.LogInformation("移除连接 {ConnectionId}，当前连接数: {ConnectionCount}", 
                connectionId, ConnectionCount);
            
            return true;
        }

        _logger?.LogWarning("连接 {ConnectionId} 不存在，无法移除", connectionId);
        return false;
    }

    /// <summary>
    /// 获取连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>客户端连接</returns>
    public ClientConnection? GetConnection(string connectionId)
    {
        ArgumentNullException.ThrowIfNull(connectionId);
        
        _connections.TryGetValue(connectionId, out var connection);
        return connection;
    }

    /// <summary>
    /// 获取所有连接
    /// </summary>
    /// <returns>所有连接</returns>
    public IReadOnlyList<ClientConnection> GetAllConnections()
    {
        return _connections.Values.ToList().AsReadOnly();
    }

    /// <summary>
    /// 获取活跃连接
    /// </summary>
    /// <returns>活跃连接</returns>
    public IReadOnlyList<ClientConnection> GetActiveConnections()
    {
        return _connections.Values
            .Where(c => c.IsConnected && c.Status == TcpServerConstants.ConnectionStates.Connected)
            .ToList()
            .AsReadOnly();
    }

    /// <summary>
    /// 获取指定IP的连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>指定IP的连接</returns>
    public IReadOnlyList<ClientConnection> GetConnectionsByIp(IPAddress ipAddress)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);
        
        return _connections.Values
            .Where(c => c.ClientIpAddress.Equals(ipAddress))
            .ToList()
            .AsReadOnly();
    }

    /// <summary>
    /// 检查连接是否存在
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否存在</returns>
    public bool HasConnection(string connectionId)
    {
        ArgumentNullException.ThrowIfNull(connectionId);
        return _connections.ContainsKey(connectionId);
    }

    /// <summary>
    /// 检查连接是否活跃
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>是否活跃</returns>
    public bool IsConnectionActive(string connectionId)
    {
        var connection = GetConnection(connectionId);
        return connection?.IsConnected == true && connection.Status == TcpServerConstants.ConnectionStates.Connected;
    }

    /// <summary>
    /// 清理断开的连接
    /// </summary>
    /// <returns>清理的连接数</returns>
    public async Task<int> CleanupDisconnectedConnectionsAsync()
    {
        var disconnectedConnections = _connections.Values
            .Where(c => !c.IsConnected || c.Status == TcpServerConstants.ConnectionStates.Disconnected)
            .ToList();

        int cleanedCount = 0;
        foreach (var connection in disconnectedConnections)
        {
            if (RemoveConnection(connection.Id))
            {
                cleanedCount++;
            }
        }

        if (cleanedCount > 0)
        {
            _logger?.LogInformation("清理了 {CleanedCount} 个断开的连接", cleanedCount);
        }

        return await Task.FromResult(cleanedCount);
    }

    /// <summary>
    /// 清理超时的连接
    /// </summary>
    /// <param name="timeoutSeconds">超时时间（秒）</param>
    /// <returns>清理的连接数</returns>
    public async Task<int> CleanupTimeoutConnectionsAsync(int timeoutSeconds)
    {
        var timeoutThreshold = DateTime.UtcNow.AddSeconds(-timeoutSeconds);
        var timeoutConnections = _connections.Values
            .Where(c => c.ConnectedAt < timeoutThreshold)
            .ToList();

        int cleanedCount = 0;
        foreach (var connection in timeoutConnections)
        {
            connection.Disconnect();
            if (RemoveConnection(connection.Id))
            {
                cleanedCount++;
            }
        }

        if (cleanedCount > 0)
        {
            _logger?.LogInformation("清理了 {CleanedCount} 个超时连接", cleanedCount);
        }

        return await Task.FromResult(cleanedCount);
    }

    /// <summary>
    /// 清理空闲的连接
    /// </summary>
    /// <param name="idleTimeoutSeconds">空闲超时时间（秒）</param>
    /// <returns>清理的连接数</returns>
    public async Task<int> CleanupIdleConnectionsAsync(int idleTimeoutSeconds)
    {
        var idleThreshold = DateTime.UtcNow.AddSeconds(-idleTimeoutSeconds);
        var idleConnections = _connections.Values
            .Where(c => c.LastActivityAt < idleThreshold)
            .ToList();

        int cleanedCount = 0;
        foreach (var connection in idleConnections)
        {
            connection.Disconnect();
            if (RemoveConnection(connection.Id))
            {
                cleanedCount++;
            }
        }

        if (cleanedCount > 0)
        {
            _logger?.LogInformation("清理了 {CleanedCount} 个空闲连接", cleanedCount);
        }

        return await Task.FromResult(cleanedCount);
    }

    /// <summary>
    /// 断开所有连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <returns>断开的连接数</returns>
    public async Task<int> DisconnectAllAsync(string? reason = null)
    {
        var allConnections = _connections.Values.ToList();
        
        foreach (var connection in allConnections)
        {
            connection.Disconnect();
        }

        // 等待一小段时间让连接正常断开
        await Task.Delay(100);

        int disconnectedCount = 0;
        foreach (var connection in allConnections)
        {
            if (RemoveConnection(connection.Id))
            {
                disconnectedCount++;
            }
        }

        _logger?.LogInformation("断开了所有连接，共 {DisconnectedCount} 个连接。原因: {Reason}", 
            disconnectedCount, reason ?? "未指定");

        return disconnectedCount;
    }

    /// <summary>
    /// 断开指定IP的所有连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="reason">断开原因</param>
    /// <returns>断开的连接数</returns>
    public async Task<int> DisconnectByIpAsync(IPAddress ipAddress, string? reason = null)
    {
        ArgumentNullException.ThrowIfNull(ipAddress);

        var ipConnections = GetConnectionsByIp(ipAddress);
        
        foreach (var connection in ipConnections)
        {
            connection.Disconnect();
        }

        // 等待一小段时间让连接正常断开
        await Task.Delay(100);

        int disconnectedCount = 0;
        foreach (var connection in ipConnections)
        {
            if (RemoveConnection(connection.Id))
            {
                disconnectedCount++;
            }
        }

        _logger?.LogInformation("断开了IP {IpAddress} 的所有连接，共 {DisconnectedCount} 个连接。原因: {Reason}", 
            ipAddress, disconnectedCount, reason ?? "未指定");

        return disconnectedCount;
    }

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    public ConnectionManagerStatistics GetStatistics()
    {
        lock (_statisticsLock)
        {
            var now = DateTime.UtcNow;
            var activeConnections = GetActiveConnections();
            var authenticatedConnections = _connections.Values.Count(c => c.IsAuthenticated);
            
            // 计算平均连接持续时间
            var avgDuration = TimeSpan.Zero;
            if (_connections.Count > 0)
            {
                var totalDuration = _connections.Values.Sum(c => c.ConnectionDuration.TotalSeconds);
                avgDuration = TimeSpan.FromSeconds(totalDuration / _connections.Count);
            }

            // 按IP分组统计
            var connectionsByIp = _connections.Values
                .GroupBy(c => c.ClientIpAddress.ToString())
                .ToDictionary(g => g.Key, g => g.Count());

            // 按状态分组统计
            var connectionsByStatus = _connections.Values
                .GroupBy(c => c.Status)
                .ToDictionary(g => g.Key, g => g.Count());

            return new ConnectionManagerStatistics
            {
                CurrentConnections = ConnectionCount,
                MaxConnections = MaxConnections,
                PeakConnections = _peakConnections,
                TotalConnections = _totalConnections,
                ActiveConnections = activeConnections.Count,
                AuthenticatedConnections = authenticatedConnections,
                UnauthenticatedConnections = ConnectionCount - authenticatedConnections,
                AverageConnectionDuration = avgDuration,
                ConnectionSuccessRate = _totalConnections > 0 ? (double)ConnectionCount / _totalConnections : 0,
                LastUpdatedAt = now,
                ConnectionsByIp = connectionsByIp,
                ConnectionsByStatus = connectionsByStatus
            };
        }
    }

    /// <summary>
    /// 更新连接活动时间
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    public void UpdateConnectionActivity(string connectionId)
    {
        var connection = GetConnection(connectionId);
        connection?.UpdateLastActivity();
    }

    /// <summary>
    /// 更新连接心跳时间
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    public void UpdateConnectionHeartbeat(string connectionId)
    {
        var connection = GetConnection(connectionId);
        connection?.UpdateLastHeartbeat();
    }

    /// <summary>
    /// 设置连接认证状态
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="isAuthenticated">是否已认证</param>
    /// <param name="user">认证用户</param>
    public void SetConnectionAuthentication(string connectionId, bool isAuthenticated, string? user = null)
    {
        var connection = GetConnection(connectionId);
        connection?.SetAuthentication(isAuthenticated, user);
    }

    /// <summary>
    /// 获取指定状态的连接
    /// </summary>
    /// <param name="status">连接状态</param>
    /// <returns>指定状态的连接</returns>
    public IReadOnlyList<ClientConnection> GetConnectionsByStatus(string status)
    {
        ArgumentNullException.ThrowIfNull(status);
        
        return _connections.Values
            .Where(c => c.Status == status)
            .ToList()
            .AsReadOnly();
    }

    /// <summary>
    /// 获取已认证的连接
    /// </summary>
    /// <returns>已认证的连接</returns>
    public IReadOnlyList<ClientConnection> GetAuthenticatedConnections()
    {
        return _connections.Values
            .Where(c => c.IsAuthenticated)
            .ToList()
            .AsReadOnly();
    }

    /// <summary>
    /// 获取未认证的连接
    /// </summary>
    /// <returns>未认证的连接</returns>
    public IReadOnlyList<ClientConnection> GetUnauthenticatedConnections()
    {
        return _connections.Values
            .Where(c => !c.IsAuthenticated)
            .ToList()
            .AsReadOnly();
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 断开所有连接
            _ = DisconnectAllAsync("服务器关闭").ConfigureAwait(false);
            
            _disposed = true;
        }
    }
}
