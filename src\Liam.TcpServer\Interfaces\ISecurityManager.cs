using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using Liam.TcpServer.Models;

namespace Liam.TcpServer.Interfaces;

/// <summary>
/// 安全管理器接口
/// </summary>
public interface ISecurityManager : IDisposable
{
    /// <summary>
    /// 安全设置
    /// </summary>
    SecuritySettings Settings { get; }

    /// <summary>
    /// 检查IP是否被允许连接
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否被允许连接</returns>
    bool IsConnectionAllowed(IPAddress ipAddress);

    /// <summary>
    /// 检查连接频率是否超限
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否超限</returns>
    bool IsConnectionRateLimited(IPAddress ipAddress);

    /// <summary>
    /// 记录连接尝试
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="isSuccess">是否成功</param>
    void RecordConnectionAttempt(IPAddress ipAddress, bool isSuccess);

    /// <summary>
    /// 添加IP到白名单
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    void AddToWhitelist(IPAddress ipAddress);

    /// <summary>
    /// 添加IP到黑名单
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="duration">黑名单持续时间（可选）</param>
    void AddToBlacklist(IPAddress ipAddress, TimeSpan? duration = null);

    /// <summary>
    /// 从白名单移除IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    void RemoveFromWhitelist(IPAddress ipAddress);

    /// <summary>
    /// 从黑名单移除IP
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    void RemoveFromBlacklist(IPAddress ipAddress);

    /// <summary>
    /// 清理过期的黑名单条目
    /// </summary>
    /// <returns>清理的条目数</returns>
    int CleanupExpiredBlacklistEntries();

    /// <summary>
    /// 验证SSL证书
    /// </summary>
    /// <param name="certificate">证书</param>
    /// <param name="chain">证书链</param>
    /// <param name="sslPolicyErrors">SSL策略错误</param>
    /// <returns>是否验证通过</returns>
    bool ValidateSslCertificate(X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors);

    /// <summary>
    /// 创建SSL流
    /// </summary>
    /// <param name="networkStream">网络流</param>
    /// <param name="serverCertificate">服务器证书</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>SSL流</returns>
    Task<SslStream> CreateSslStreamAsync(Stream networkStream, X509Certificate2 serverCertificate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证客户端认证
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="credentials">认证凭据</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> ValidateAuthenticationAsync(ClientConnection connection, AuthenticationCredentials credentials);

    /// <summary>
    /// 加密消息
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>加密后的数据</returns>
    byte[] EncryptMessage(byte[] data);

    /// <summary>
    /// 解密消息
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <returns>解密后的数据</returns>
    byte[] DecryptMessage(byte[] encryptedData);

    /// <summary>
    /// 生成认证令牌
    /// </summary>
    /// <param name="user">用户</param>
    /// <param name="expiresAt">过期时间</param>
    /// <returns>认证令牌</returns>
    string GenerateAuthenticationToken(string user, DateTime? expiresAt = null);

    /// <summary>
    /// 验证认证令牌
    /// </summary>
    /// <param name="token">认证令牌</param>
    /// <returns>令牌验证结果</returns>
    TokenValidationResult ValidateAuthenticationToken(string token);

    /// <summary>
    /// 获取安全统计信息
    /// </summary>
    /// <returns>安全统计信息</returns>
    SecurityStatistics GetStatistics();

    /// <summary>
    /// 检查连接是否需要认证
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>是否需要认证</returns>
    bool RequiresAuthentication(ClientConnection connection);

    /// <summary>
    /// 检查连接认证是否过期
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <returns>是否过期</returns>
    bool IsAuthenticationExpired(ClientConnection connection);
}

/// <summary>
/// 认证凭据
/// </summary>
public class AuthenticationCredentials
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// 认证密钥
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// 认证令牌
    /// </summary>
    public string? Token { get; set; }

    /// <summary>
    /// 客户端证书
    /// </summary>
    public X509Certificate2? ClientCertificate { get; set; }

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 获取属性
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="key">属性键</param>
    /// <returns>属性值</returns>
    public T? GetProperty<T>(string key)
    {
        if (Properties.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    /// <summary>
    /// 设置属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    public void SetProperty(string key, object value)
    {
        Properties[key] = value;
    }
}

/// <summary>
/// 令牌验证结果
/// </summary>
public class TokenValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 用户
    /// </summary>
    public string? User { get; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; }

    /// <summary>
    /// 初始化令牌验证结果
    /// </summary>
    /// <param name="isValid">是否验证通过</param>
    /// <param name="user">用户</param>
    /// <param name="expiresAt">过期时间</param>
    /// <param name="isExpired">是否已过期</param>
    /// <param name="errorMessage">错误消息</param>
    public TokenValidationResult(bool isValid, string? user = null, DateTime? expiresAt = null, bool isExpired = false, string? errorMessage = null)
    {
        IsValid = isValid;
        User = user;
        ExpiresAt = expiresAt;
        IsExpired = isExpired;
        ErrorMessage = errorMessage;
    }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <param name="user">用户</param>
    /// <param name="expiresAt">过期时间</param>
    /// <returns>成功的验证结果</returns>
    public static TokenValidationResult Success(string user, DateTime? expiresAt = null)
    {
        return new TokenValidationResult(true, user, expiresAt);
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="isExpired">是否已过期</param>
    /// <returns>失败的验证结果</returns>
    public static TokenValidationResult Failure(string errorMessage, bool isExpired = false)
    {
        return new TokenValidationResult(false, null, null, isExpired, errorMessage);
    }
}

/// <summary>
/// 安全统计信息
/// </summary>
public class SecurityStatistics
{
    /// <summary>
    /// 总连接尝试数
    /// </summary>
    public long TotalConnectionAttempts { get; set; }

    /// <summary>
    /// 成功连接数
    /// </summary>
    public long SuccessfulConnections { get; set; }

    /// <summary>
    /// 被拒绝的连接数
    /// </summary>
    public long RejectedConnections { get; set; }

    /// <summary>
    /// 黑名单拦截数
    /// </summary>
    public long BlacklistBlocks { get; set; }

    /// <summary>
    /// 白名单通过数
    /// </summary>
    public long WhitelistPasses { get; set; }

    /// <summary>
    /// 频率限制拦截数
    /// </summary>
    public long RateLimitBlocks { get; set; }

    /// <summary>
    /// 认证成功数
    /// </summary>
    public long AuthenticationSuccesses { get; set; }

    /// <summary>
    /// 认证失败数
    /// </summary>
    public long AuthenticationFailures { get; set; }

    /// <summary>
    /// SSL握手成功数
    /// </summary>
    public long SslHandshakeSuccesses { get; set; }

    /// <summary>
    /// SSL握手失败数
    /// </summary>
    public long SslHandshakeFailures { get; set; }

    /// <summary>
    /// 当前白名单条目数
    /// </summary>
    public int WhitelistEntries { get; set; }

    /// <summary>
    /// 当前黑名单条目数
    /// </summary>
    public int BlacklistEntries { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }

    /// <summary>
    /// 连接成功率
    /// </summary>
    public double ConnectionSuccessRate => TotalConnectionAttempts > 0 ? (double)SuccessfulConnections / TotalConnectionAttempts : 0;

    /// <summary>
    /// 认证成功率
    /// </summary>
    public double AuthenticationSuccessRate => (AuthenticationSuccesses + AuthenticationFailures) > 0 ? (double)AuthenticationSuccesses / (AuthenticationSuccesses + AuthenticationFailures) : 0;

    /// <summary>
    /// SSL握手成功率
    /// </summary>
    public double SslHandshakeSuccessRate => (SslHandshakeSuccesses + SslHandshakeFailures) > 0 ? (double)SslHandshakeSuccesses / (SslHandshakeSuccesses + SslHandshakeFailures) : 0;
}
