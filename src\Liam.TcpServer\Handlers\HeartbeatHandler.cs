using Microsoft.Extensions.Logging;
using Liam.TcpServer.Constants;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;

namespace Liam.TcpServer.Handlers;

/// <summary>
/// 心跳处理器
/// </summary>
public class HeartbeatHandler : IDisposable
{
    private readonly IConnectionManager _connectionManager;
    private readonly ILogger<HeartbeatHandler>? _logger;
    private readonly Timer? _heartbeatTimer;
    private readonly Timer? _timeoutCheckTimer;
    private readonly TcpServerConfig _config;
    private bool _disposed = false;

    /// <summary>
    /// 心跳发送事件
    /// </summary>
    public event EventHandler<HeartbeatSentEventArgs>? HeartbeatSent;

    /// <summary>
    /// 心跳超时事件
    /// </summary>
    public event EventHandler<HeartbeatTimeoutEventArgs>? HeartbeatTimeout;

    /// <summary>
    /// 初始化心跳处理器
    /// </summary>
    /// <param name="connectionManager">连接管理器</param>
    /// <param name="config">服务器配置</param>
    /// <param name="logger">日志记录器</param>
    public HeartbeatHandler(IConnectionManager connectionManager, TcpServerConfig config, ILogger<HeartbeatHandler>? logger = null)
    {
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = logger;

        if (_config.EnableHeartbeat)
        {
            // 创建心跳发送定时器
            var heartbeatInterval = TimeSpan.FromSeconds(_config.HeartbeatIntervalSeconds);
            _heartbeatTimer = new Timer(SendHeartbeatsCallback, null, heartbeatInterval, heartbeatInterval);

            // 创建心跳超时检查定时器
            var timeoutCheckInterval = TimeSpan.FromSeconds(_config.HeartbeatTimeoutSeconds / 2);
            _timeoutCheckTimer = new Timer(CheckHeartbeatTimeoutsCallback, null, timeoutCheckInterval, timeoutCheckInterval);

            _logger?.LogInformation("心跳处理器已启动，间隔: {Interval}秒, 超时: {Timeout}秒", 
                _config.HeartbeatIntervalSeconds, _config.HeartbeatTimeoutSeconds);
        }
    }

    /// <summary>
    /// 发送心跳到指定连接
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task<bool> SendHeartbeatAsync(ClientConnection connection, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(connection);

        if (!connection.IsConnected)
        {
            return false;
        }

        try
        {
            var heartbeatMessage = TcpMessage.CreateHeartbeatRequest();
            var data = heartbeatMessage.Serialize();

            await connection.NetworkStream.WriteAsync(data, cancellationToken);
            await connection.NetworkStream.FlushAsync(cancellationToken);

            connection.Statistics.RecordHeartbeatSent();
            connection.UpdateLastHeartbeat();

            _logger?.LogDebug("发送心跳到连接 {ConnectionId}", connection.Id);

            // 触发心跳发送事件
            HeartbeatSent?.Invoke(this, new HeartbeatSentEventArgs(connection));

            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "发送心跳到连接 {ConnectionId} 失败", connection.Id);
            return false;
        }
    }

    /// <summary>
    /// 发送心跳到所有活跃连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的数量</returns>
    public async Task<int> SendHeartbeatToAllAsync(CancellationToken cancellationToken = default)
    {
        var activeConnections = _connectionManager.GetActiveConnections();
        int successCount = 0;

        var tasks = activeConnections.Select(async connection =>
        {
            if (await SendHeartbeatAsync(connection, cancellationToken))
            {
                Interlocked.Increment(ref successCount);
            }
        });

        await Task.WhenAll(tasks);

        _logger?.LogDebug("发送心跳到 {TotalConnections} 个连接，成功 {SuccessCount} 个", 
            activeConnections.Count, successCount);

        return successCount;
    }

    /// <summary>
    /// 处理心跳响应
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="message">心跳响应消息</param>
    public void HandleHeartbeatResponse(ClientConnection connection, TcpMessage message)
    {
        ArgumentNullException.ThrowIfNull(connection);
        ArgumentNullException.ThrowIfNull(message);

        if (message.MessageType == TcpServerConstants.MessageTypes.HeartbeatResponse)
        {
            connection.Statistics.RecordHeartbeatReceived();
            connection.UpdateLastHeartbeat();

            _logger?.LogDebug("收到连接 {ConnectionId} 的心跳响应", connection.Id);
        }
    }

    /// <summary>
    /// 检查心跳超时的连接
    /// </summary>
    /// <returns>超时的连接列表</returns>
    public List<ClientConnection> CheckHeartbeatTimeouts()
    {
        var timeoutThreshold = DateTime.UtcNow.AddSeconds(-_config.HeartbeatTimeoutSeconds);
        var activeConnections = _connectionManager.GetActiveConnections();
        var timeoutConnections = new List<ClientConnection>();

        foreach (var connection in activeConnections)
        {
            if (connection.LastHeartbeatAt < timeoutThreshold)
            {
                timeoutConnections.Add(connection);
                connection.Statistics.RecordHeartbeatTimeout();

                _logger?.LogWarning("连接 {ConnectionId} 心跳超时，最后心跳时间: {LastHeartbeat}", 
                    connection.Id, connection.LastHeartbeatAt);

                // 触发心跳超时事件
                HeartbeatTimeout?.Invoke(this, new HeartbeatTimeoutEventArgs(connection));
            }
        }

        return timeoutConnections;
    }

    /// <summary>
    /// 断开心跳超时的连接
    /// </summary>
    /// <returns>断开的连接数</returns>
    public async Task<int> DisconnectTimeoutConnectionsAsync()
    {
        var timeoutConnections = CheckHeartbeatTimeouts();
        
        foreach (var connection in timeoutConnections)
        {
            try
            {
                connection.Disconnect();
                _logger?.LogInformation("断开心跳超时的连接 {ConnectionId}", connection.Id);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "断开超时连接 {ConnectionId} 时发生异常", connection.Id);
            }
        }

        return await Task.FromResult(timeoutConnections.Count);
    }

    /// <summary>
    /// 获取心跳统计信息
    /// </summary>
    /// <returns>心跳统计信息</returns>
    public HeartbeatStatistics GetStatistics()
    {
        var activeConnections = _connectionManager.GetActiveConnections();
        var now = DateTime.UtcNow;
        var timeoutThreshold = now.AddSeconds(-_config.HeartbeatTimeoutSeconds);

        var totalHeartbeatsSent = activeConnections.Sum(c => c.Statistics.HeartbeatsSent);
        var totalHeartbeatsReceived = activeConnections.Sum(c => c.Statistics.HeartbeatsReceived);
        var totalTimeouts = activeConnections.Sum(c => c.Statistics.HeartbeatTimeouts);
        var timeoutConnections = activeConnections.Count(c => c.LastHeartbeatAt < timeoutThreshold);

        return new HeartbeatStatistics
        {
            TotalHeartbeatsSent = totalHeartbeatsSent,
            TotalHeartbeatsReceived = totalHeartbeatsReceived,
            TotalTimeouts = totalTimeouts,
            ActiveConnections = activeConnections.Count,
            TimeoutConnections = timeoutConnections,
            HeartbeatInterval = TimeSpan.FromSeconds(_config.HeartbeatIntervalSeconds),
            HeartbeatTimeout = TimeSpan.FromSeconds(_config.HeartbeatTimeoutSeconds),
            LastUpdatedAt = now
        };
    }

    /// <summary>
    /// 心跳发送定时器回调
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void SendHeartbeatsCallback(object? state)
    {
        if (_disposed || !_config.EnableHeartbeat)
        {
            return;
        }

        try
        {
            await SendHeartbeatToAllAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送心跳时发生异常");
        }
    }

    /// <summary>
    /// 心跳超时检查定时器回调
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void CheckHeartbeatTimeoutsCallback(object? state)
    {
        if (_disposed || !_config.EnableHeartbeat)
        {
            return;
        }

        try
        {
            await DisconnectTimeoutConnectionsAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "检查心跳超时时发生异常");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _heartbeatTimer?.Dispose();
            _timeoutCheckTimer?.Dispose();
            _disposed = true;

            _logger?.LogInformation("心跳处理器已停止");
        }
    }
}

/// <summary>
/// 心跳发送事件参数
/// </summary>
public class HeartbeatSentEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SentAt { get; }

    /// <summary>
    /// 初始化心跳发送事件参数
    /// </summary>
    /// <param name="connection">客户端连接</param>
    public HeartbeatSentEventArgs(ClientConnection connection)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        SentAt = DateTime.UtcNow;
    }
}

/// <summary>
/// 心跳超时事件参数
/// </summary>
public class HeartbeatTimeoutEventArgs : EventArgs
{
    /// <summary>
    /// 客户端连接
    /// </summary>
    public ClientConnection Connection { get; }

    /// <summary>
    /// 超时时间
    /// </summary>
    public DateTime TimeoutAt { get; }

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime LastHeartbeatAt { get; }

    /// <summary>
    /// 初始化心跳超时事件参数
    /// </summary>
    /// <param name="connection">客户端连接</param>
    public HeartbeatTimeoutEventArgs(ClientConnection connection)
    {
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        TimeoutAt = DateTime.UtcNow;
        LastHeartbeatAt = connection.LastHeartbeatAt;
    }
}

/// <summary>
/// 心跳统计信息
/// </summary>
public class HeartbeatStatistics
{
    /// <summary>
    /// 总发送心跳数
    /// </summary>
    public long TotalHeartbeatsSent { get; set; }

    /// <summary>
    /// 总接收心跳数
    /// </summary>
    public long TotalHeartbeatsReceived { get; set; }

    /// <summary>
    /// 总超时数
    /// </summary>
    public long TotalTimeouts { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 超时连接数
    /// </summary>
    public int TimeoutConnections { get; set; }

    /// <summary>
    /// 心跳间隔
    /// </summary>
    public TimeSpan HeartbeatInterval { get; set; }

    /// <summary>
    /// 心跳超时时间
    /// </summary>
    public TimeSpan HeartbeatTimeout { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }

    /// <summary>
    /// 心跳成功率
    /// </summary>
    public double HeartbeatSuccessRate => TotalHeartbeatsSent > 0 ? (double)TotalHeartbeatsReceived / TotalHeartbeatsSent : 0;

    /// <summary>
    /// 超时率
    /// </summary>
    public double TimeoutRate => TotalHeartbeatsSent > 0 ? (double)TotalTimeouts / TotalHeartbeatsSent : 0;
}
